import { z } from 'zod';

// ============================================================================
// TRANSACTION ZOD VALIDATION SCHEMAS
// ============================================================================

export const transactionFiltersSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  status: z.enum(['paid', 'failed', 'pending', 'all']).optional().default('all'),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: "Start date must be before or equal to end date",
  path: ["endDate"],
});

export const transactionIdSchema = z.object({
  transactionId: z.string().min(1, "Transaction ID is required"),
});
