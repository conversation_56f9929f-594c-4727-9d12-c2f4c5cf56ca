'use client';

import React, { useState, useTransition } from 'react';
import { ITransactionFilters, ITransaction } from '@/types/transaction';
import { getTransactions } from '@/actions/transaction.action';
import TransactionFilterForm from './TransactionFilterForm';

interface BillingHistoryClientProps {
  initialTransactions: ITransaction[];
  children: (props: {
    filteredTransactions: ITransaction[];
    isPending: boolean;
    TransactionFilterComponent: React.ReactNode;
  }) => React.ReactNode;
}

export default function BillingHistoryClient({ 
  initialTransactions, 
  children 
}: BillingHistoryClientProps) {
  const [transactions, setTransactions] = useState<ITransaction[]>(initialTransactions);
  const [isPending, startTransition] = useTransition();

  const handleFilterSubmit = (filters: ITransactionFilters) => {
    startTransition(async () => {
      try {
        const result = await getTransactions(filters);
        if (result.success && result.data && 'items' in result.data) {
          setTransactions(result.data.items);
        }
      } catch (error) {
        console.error('Failed to filter transactions:', error);
        // You could add toast notification here
      }
    });
  };

  const handleClearFilters = () => {
    startTransition(async () => {
      try {
        const result = await getTransactions({});
        if (result.success && result.data && 'items' in result.data) {
          setTransactions(result.data.items);
        }
      } catch (error) {
        console.error('Failed to clear filters:', error);
      }
    });
  };

  const TransactionFilterComponent = (
    <TransactionFilterForm 
      onFilterSubmit={handleFilterSubmit}
      onClearFilters={handleClearFilters}
      className="mb-6"
    />
  );

  return (
    <>
      {children({
        filteredTransactions: transactions,
        isPending,
        TransactionFilterComponent,
      })}
    </>
  );
} 