// ============================================================================
// TRANSACTION TYPESCRIPT INTERFACES
// ============================================================================

export interface ITransaction {
  id: string;
  date: string; // ISO date string
  description: string;
  amount: number; // in cents
  currency: string;
  status: 'paid' | 'failed' | 'pending';
  paymentMethod: string;
  invoiceUrl?: string;
  receiptUrl?: string;
  type?: string; // additional field for transaction type
}

export interface ITransactionFilters {
  startDate?: string; // ISO date string
  endDate?: string; // ISO date string
  status?: 'paid' | 'failed' | 'pending' | 'all';
  page?: number;
  limit?: number;
}

export interface ITransactionPagination {
  items: ITransaction[];
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface ITransactionActionResult {
  success: boolean;
  data?: ITransactionPagination | ITransaction;
  message?: string;
  errors?: Record<string, string[]>;
}
